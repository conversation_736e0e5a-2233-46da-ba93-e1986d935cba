'use client';

import { useState, useCallback, useMemo, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useChat } from '@ai-sdk/react';
import { DefaultChatTransport } from 'ai';
import { MessageList } from './message-list';
import { ChatInput } from './chat-input';
import { useChatDetail } from '@/lib/hooks/use-chats';
import {
  Conversation,
  ConversationContent,
  ConversationScrollButton,
} from '@/components/ai-elements/conversation';
import { Message, MessageContent } from '@/components/ai-elements/message';
import {
  PromptInput,
  PromptInputButton,
  PromptInputModelSelect,
  PromptInputModelSelectContent,
  PromptInputModelSelectItem,
  PromptInputModelSelectTrigger,
  PromptInputModelSelectValue,
  PromptInputSubmit,
  PromptInputTextarea,
  PromptInputToolbar,
  PromptInputTools,
} from '@/components/ai-elements/prompt-input';
import { Response } from '@/components/ai-elements/response';
import { GlobeIcon } from 'lucide-react';
import {
  Source,
  Sources,
  SourcesContent,
  SourcesTrigger,
} from '@/components/ai-elements/source';
import {
  Reasoning,
  ReasoningContent,
  ReasoningTrigger,
} from '@/components/ai-elements/reasoning';
import { usePromptFromUrl } from '@/lib/hooks/use-prompt-from-url';
import { toast } from 'sonner';

import { useChatStore } from '@/lib/stores/chat-store';

interface ChatContentProps {
  initialChatId?: string;
}

export function ChatContent({ initialChatId }: ChatContentProps) {
  const router = useRouter();
  const params = useParams();
  const [generatedChatId, setGeneratedChatId] = useState<string>('');
  const [input, setInput] = useState<string>('');
  const { addPendingChat, removePendingChat, selectedModel, setSelectedModel, generateSmartTitle } = useChatStore();

  // 处理 URL 中的 prompt 参数
  const { prompt: urlPrompt, isLoading: isLoadingPrompt, hasPromptParam } = usePromptFromUrl();

  // 从 URL 参数获取当前 chatId，优先使用 URL 参数
  const urlChatId = params?.chatId as string;
  const currentChatId = urlChatId || initialChatId;

  // 为新对话生成稳定的 chatId
  const effectiveChatId = useMemo(() => {
    if (currentChatId) {
      return currentChatId;
    }
    if (!generatedChatId) {
      const newId = crypto.randomUUID();
      setGeneratedChatId(newId);
      return newId;
    }
    return generatedChatId;
  }, [currentChatId, generatedChatId]);

  // 使用 React Query 获取聊天详情
  const {
    initialMessages,
    isLoading: isLoadingMessages,
    error: chatDetailError,
  } = useChatDetail(currentChatId);

  // 使用 useChat hook - 统一配置，使用稳定的 effectiveChatId
  const {
    messages,
    status,
    error,
    sendMessage,
    stop,
    regenerate,
    setMessages,
  } = useChat({
    id: effectiveChatId, // 使用稳定的 effectiveChatId 作为 hook 的 id
    messages: initialMessages,
    transport: new DefaultChatTransport({
      api: '/api/chat',
      credentials: 'include',
      body: {
        chatId: effectiveChatId,
        modelId: selectedModel, // 传递选中的模型ID
      },
    }),
    experimental_throttle: 50,
    onFinish: async () => {
      try {
        const baseMessageCount = initialMessages?.length || 0;

        // 检查是否是首次对话：如果基础消息数为0，说明这是新对话的第一轮
        const isFirstConversation = baseMessageCount === 0;
        if (isFirstConversation) {
          generateSmartTitle(effectiveChatId);
        }

      } catch (error) {
        console.error('❌ 处理聊天完成事件失败:', error);
        // 出错时回退到移除临时记录
        removePendingChat(effectiveChatId);
      }
    },
    onError: (error) => {
      // 移除临时聊天记录（如果存在）
      removePendingChat(effectiveChatId);

      // 检查是否是限额错误
      if (error.message && (
        error.message.includes('已达到今日使用限额') ||
        error.message.includes('已达到今日免费使用限制')
      )) {
      } else {
        toast.error('发送消息失败，请重试');
      }
    },
  });



  // 处理 URL 中的 prompt 参数 - 成功获取后清理 URL
  useEffect(() => {
    if (urlPrompt && hasPromptParam && !isLoadingPrompt) {
      // 成功获取到 prompt 数据后，清理 URL 参数以避免重复触发
      console.log('✅ 成功获取 URL prompt，清理 URL 参数');
      router.replace('/chat', { scroll: false });
    }
  }, [urlPrompt, hasPromptParam, isLoadingPrompt, router]);

  // 处理输入变化
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  }, []);

  // 处理表单提交
  const handleSubmit = useCallback(async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!input.trim()) return;

    // 如果是新对话，添加临时聊天记录到侧边栏
    if (!urlChatId) {
      addPendingChat(effectiveChatId, '新的对话');
      // 立即跳转到新对话页面
      router.replace(`/chat/${effectiveChatId}`);
    }

    // 发送消息
    try {
      await sendMessage({ text: input.trim() });
      setInput(''); // 清空输入框
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  }, [input, sendMessage, router, effectiveChatId, urlChatId, addPendingChat]);

  // 处理 append 功能（用于兼容现有的 ChatInput 组件）
  const append = useCallback(async (message: { role: 'user'; content: string }): Promise<string | null | undefined> => {
    try {
      await sendMessage({ text: message.content });
      return message.content; // 返回发送的消息内容
    } catch (error) {
      console.error('发送消息失败:', error);
      return null;
    }
  }, [sendMessage]);

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* 消息列表 */}
      <div className="flex-1 min-h-0 overflow-hidden">
        {/* <MessageList
          messages={messages}
          isLoading={isLoadingMessages}
          error={error || chatDetailError}
          status={status}
          onRetry={regenerate}
        /> */}
        <ConversationContent>
          {messages.map((message) => (
            <Message from={message.role} key={message.id}>
              <MessageContent>
                {message.parts.map((part, i) => {
                  switch (part.type) {
                    case 'text':
                      return (
                        <Response key={`${message.id}-${i}`}>
                          {part.text}
                        </Response>
                      );
                    case 'reasoning':
                      return (
                        <Reasoning
                          key={`${message.id}-${i}`}
                          className="w-full"
                          isStreaming={status === 'streaming'}
                        >
                          <ReasoningTrigger />
                          <ReasoningContent>{part.text}</ReasoningContent>
                        </Reasoning>
                      );
                    default:
                      return null;
                  }
                })}
              </MessageContent>
            </Message>))}
        </ConversationContent>
      </div>

      {/* 输入区域 */}
      <div className="border-t bg-background p-4 flex-shrink-0">
        <div className="max-w-4xl mx-auto">
          <ChatInput
            input={input}
            handleInputChange={handleInputChange}
            handleSubmit={handleSubmit}
            setInput={setInput}
            append={append}
            status={status}
            isLoading={status === 'submitted'}
            isStreaming={status === 'streaming'}
            onStop={stop}
            placeholder={urlChatId ? "开始对话，输入 / 快捷选择 Prompt" : "开始对话，输入 / 快捷选择 Prompt"}
            selectedModel={selectedModel}
            onModelChange={setSelectedModel}
            initialPrompt={urlPrompt}
          />
        </div>
      </div>
    </div>
  );
}
